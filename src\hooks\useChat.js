import { useEffect, useCallback, useRef } from 'react';
import useChatStore from '../store/useChatStore.js';
import useAuthStore from '../store/useAuthStore.js';
import { CHAT_CONFIG } from '../config/chat.config.js';

/**
 * Custom hook for chat functionality
 * Provides a simplified interface for chat operations
 */
export const useChat = (options = {}) => {
  const {
    autoConnect = true,
    autoFetchChats = true,
    enableNotifications = true,
  } = options;

  const { user, access_token } = useAuthStore();
  const {
    // Connection state
    isConnected,
    isConnecting,
    connectionError,
    connect,
    disconnect,
    
    // Chat data
    chats,
    chatsLoading,
    chatsError,
    chatsHasMore,
    fetchChats,
    createChat,
    
    // Messages
    messages,
    messagesLoading,
    messagesError,
    messagesHasMore,
    fetchMessages,
    sendMessage,
    markAsRead,
    
    // UI state
    activeChatId,
    setActiveChat,
    typingUsers,
    totalUnreadCount,
    
    // Typing
    startTyping,
    stopTyping,
    
    // Utilities
    getChatById,
    getUnreadCount,
    requestNotificationPermission,
    reset,
  } = useChatStore();

  const initializationRef = useRef(false);

  // Initialize chat system
  useEffect(() => {
    if (!user || !access_token || initializationRef.current) return;

    const initialize = async () => {
      try {
        // Request notification permission if enabled
        if (enableNotifications) {
          await requestNotificationPermission();
        }

        // Connect to WebSocket if auto-connect is enabled
        if (autoConnect) {
          connect();
        }

        // Fetch initial chats if auto-fetch is enabled
        if (autoFetchChats) {
          await fetchChats();
        }

        initializationRef.current = true;
      } catch (error) {
        console.error('Failed to initialize chat system:', error);
      }
    };

    initialize();
  }, [
    user,
    access_token,
    autoConnect,
    autoFetchChats,
    enableNotifications,
    connect,
    fetchChats,
    requestNotificationPermission,
  ]);

  // Cleanup on unmount or user change
  useEffect(() => {
    return () => {
      if (initializationRef.current) {
        disconnect();
        initializationRef.current = false;
      }
    };
  }, [disconnect, user?.id]);

  // Auto-fetch messages when active chat changes
  useEffect(() => {
    if (activeChatId && !messages[activeChatId]) {
      fetchMessages(activeChatId);
    }
  }, [activeChatId, messages, fetchMessages]);

  /**
   * Send a message to the active chat
   */
  const sendMessageToActiveChat = useCallback(async (content) => {
    if (!activeChatId) {
      throw new Error('No active chat selected');
    }
    return sendMessage(activeChatId, content);
  }, [activeChatId, sendMessage]);

  /**
   * Mark active chat as read
   */
  const markActiveChatAsRead = useCallback(async () => {
    if (!activeChatId) return;
    return markAsRead(activeChatId);
  }, [activeChatId, markAsRead]);

  /**
   * Start typing in active chat
   */
  const startTypingInActiveChat = useCallback(() => {
    if (!activeChatId) return;
    startTyping(activeChatId);
  }, [activeChatId, startTyping]);

  /**
   * Stop typing in active chat
   */
  const stopTypingInActiveChat = useCallback(() => {
    if (!activeChatId) return;
    stopTyping(activeChatId);
  }, [activeChatId, stopTyping]);

  /**
   * Get messages for active chat
   */
  const getActiveChatMessages = useCallback(() => {
    return activeChatId ? messages[activeChatId] || [] : [];
  }, [activeChatId, messages]);

  /**
   * Get typing users for active chat
   */
  const getActiveChatTypingUsers = useCallback(() => {
    return activeChatId ? typingUsers[activeChatId] || [] : [];
  }, [activeChatId, typingUsers]);

  /**
   * Get active chat object
   */
  const getActiveChat = useCallback(() => {
    return activeChatId ? getChatById(activeChatId) : null;
  }, [activeChatId, getChatById]);

  /**
   * Load more chats
   */
  const loadMoreChats = useCallback(() => {
    if (chatsHasMore && !chatsLoading) {
      const cursor = useChatStore.getState().chatsCursor;
      return fetchChats(cursor);
    }
  }, [chatsHasMore, chatsLoading, fetchChats]);

  /**
   * Load more messages for active chat
   */
  const loadMoreMessages = useCallback(() => {
    if (!activeChatId) return;
    
    const hasMore = messagesHasMore[activeChatId];
    const loading = messagesLoading[activeChatId];
    
    if (hasMore && !loading) {
      const cursor = useChatStore.getState().messagesCursor[activeChatId];
      return fetchMessages(activeChatId, cursor);
    }
  }, [activeChatId, messagesHasMore, messagesLoading, fetchMessages]);

  /**
   * Create a new direct chat with a user
   */
  const createDirectChat = useCallback(async (userId) => {
    return createChat({
      type: 'direct',
      participantIds: [userId],
    });
  }, [createChat]);

  /**
   * Create a new group chat
   */
  const createGroupChat = useCallback(async (name, participantIds, description) => {
    return createChat({
      type: 'group',
      participantIds,
      name,
      description,
    });
  }, [createChat]);

  /**
   * Get connection status information
   */
  const getConnectionStatus = useCallback(() => {
    return {
      isConnected,
      isConnecting,
      error: connectionError,
      status: isConnected ? 'connected' : 
              isConnecting ? 'connecting' : 
              connectionError ? 'error' : 'disconnected',
    };
  }, [isConnected, isConnecting, connectionError]);

  /**
   * Get chat statistics
   */
  const getChatStats = useCallback(() => {
    return {
      totalChats: chats.length,
      totalUnreadCount,
      activeChatId,
      hasActiveChat: !!activeChatId,
      connectionStatus: getConnectionStatus().status,
    };
  }, [chats.length, totalUnreadCount, activeChatId, getConnectionStatus]);

  /**
   * Manually reconnect
   */
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(() => {
      connect();
    }, 1000);
  }, [disconnect, connect]);

  /**
   * Reset chat system
   */
  const resetChat = useCallback(() => {
    reset();
    initializationRef.current = false;
  }, [reset]);

  return {
    // Connection state
    isConnected,
    isConnecting,
    connectionError,
    connectionStatus: getConnectionStatus(),
    
    // Chat data
    chats,
    chatsLoading,
    chatsError,
    chatsHasMore,
    
    // Messages
    messages: getActiveChatMessages(),
    messagesLoading: activeChatId ? messagesLoading[activeChatId] || false : false,
    messagesError: activeChatId ? messagesError[activeChatId] : null,
    messagesHasMore: activeChatId ? messagesHasMore[activeChatId] || false : false,
    
    // Active chat
    activeChatId,
    activeChat: getActiveChat(),
    typingUsers: getActiveChatTypingUsers(),
    
    // Statistics
    totalUnreadCount,
    unreadCount: getUnreadCount(),
    stats: getChatStats(),
    
    // Actions
    setActiveChat,
    sendMessage: sendMessageToActiveChat,
    markAsRead: markActiveChatAsRead,
    startTyping: startTypingInActiveChat,
    stopTyping: stopTypingInActiveChat,
    
    // Chat management
    createChat,
    createDirectChat,
    createGroupChat,
    
    // Data loading
    fetchChats,
    fetchMessages: (chatId, cursor) => fetchMessages(chatId, cursor),
    loadMoreChats,
    loadMoreMessages,
    
    // Connection management
    connect,
    disconnect,
    reconnect,
    reset: resetChat,
    
    // Utilities
    getChatById,
    requestNotificationPermission,
  };
};

/**
 * Hook for chat notifications
 */
export const useChatNotifications = () => {
  const { totalUnreadCount } = useChatStore();
  const { requestNotificationPermission } = useChatStore();

  useEffect(() => {
    // Update document title with unread count
    const originalTitle = document.title.replace(/^\(\d+\)\s*/, '');
    
    if (totalUnreadCount > 0) {
      document.title = `(${totalUnreadCount}) ${originalTitle}`;
    } else {
      document.title = originalTitle;
    }

    return () => {
      document.title = originalTitle;
    };
  }, [totalUnreadCount]);

  return {
    totalUnreadCount,
    hasUnread: totalUnreadCount > 0,
    requestNotificationPermission,
  };
};

/**
 * Hook for chat connection monitoring
 */
export const useChatConnection = () => {
  const { 
    isConnected, 
    isConnecting, 
    connectionError, 
    connect, 
    disconnect 
  } = useChatStore();

  const status = isConnected ? 'connected' : 
                 isConnecting ? 'connecting' : 
                 connectionError ? 'error' : 'disconnected';

  return {
    isConnected,
    isConnecting,
    connectionError,
    status,
    connect,
    disconnect,
  };
};

export default useChat;
