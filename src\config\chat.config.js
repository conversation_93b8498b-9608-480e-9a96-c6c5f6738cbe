/**
 * Chat System Configuration
 *
 * This file contains all configurable values for the chat system.
 * Modify these values to customize chat behavior without changing code.
 */

export const CHAT_CONFIG = {
  // WebSocket Configuration
  WEBSOCKET: {
    NAMESPACE: '/chat',
    AUTO_CONNECT: false, // Changed to false to prevent automatic connections
    RECONNECTION: true,
    RECONNECTION_ATTEMPTS: 3, // Reduced from 5 to 3
    RECONNECTION_DELAY: 2000, // Increased from 1000 to 2000ms
    RECONNECTION_DELAY_MAX: 10000, // Reduced from 30000 to 10000ms
    TIMEOUT: 15000, // Reduced from 20000 to 15000ms
    MAX_CONCURRENT_CONNECTIONS: 1, // Limit to 1 connection per user
  },

  // Message Configuration
  MESSAGES: {
    MAX_LENGTH: 2000,
    MIN_LENGTH: 1,
    PAGE_SIZE: 50,
    MAX_PAGE_SIZE: 100,
    TYPING_TIMEOUT: 3000, // 3 seconds
    TYPING_DEBOUNCE: 300, // 300ms
  },

  // Chat Configuration
  CHATS: {
    MAX_NAME_LENGTH: 100,
    MIN_NAME_LENGTH: 1,
    MAX_PARTICIPANTS: 50,
    PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
  },

  // UI Configuration
  UI: {
    CHAT_MODAL_MAX_HEIGHT: '80vh',
    MESSAGE_LIST_HEIGHT: '400px',
    CHAT_LIST_HEIGHT: '300px',
    SCROLL_THRESHOLD: 100, // pixels from top to trigger load more
    ANIMATION_DURATION: 200, // milliseconds
  },

  // Rate Limiting (client-side enforcement)
  RATE_LIMITS: {
    WEBSOCKET_MESSAGES_PER_MINUTE: 10,
    HTTP_MESSAGES_PER_MINUTE: 30,
    TYPING_EVENTS_PER_MINUTE: 60,
  },

  // Connection Health
  HEALTH: {
    HEARTBEAT_INTERVAL: 30000, // 30 seconds
    CONNECTION_TIMEOUT: 10000, // 10 seconds
    MAX_MISSED_HEARTBEATS: 3,
  },

  // Caching
  CACHE: {
    MAX_MESSAGES_PER_CHAT: 500,
    MAX_CHATS_CACHED: 100,
    CACHE_EXPIRY: 5 * 60 * 1000, // 5 minutes
  },

  // Notifications
  NOTIFICATIONS: {
    SHOW_DESKTOP_NOTIFICATIONS: true,
    SHOW_TOAST_NOTIFICATIONS: true,
    SOUND_ENABLED: false, // Can be enabled later
    NOTIFICATION_TIMEOUT: 5000, // 5 seconds
  },

  // Development Settings
  DEV: {
    ENABLE_DEBUG_LOGS: import.meta.env.NODE_ENV === 'development',
    MOCK_DATA: import.meta.env.VITE_USE_MOCK_CHAT_DATA === 'true',
    DISABLE_RATE_LIMITING: import.meta.env.NODE_ENV === 'development',
  },

  // API Endpoints
  API: {
    BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
    CHATS_ENDPOINT: '/chats',
    MESSAGES_ENDPOINT: (chatId) => `/chats/${chatId}/messages`,
    SEND_MESSAGE_ENDPOINT: (chatId) => `/chats/${chatId}/messages`,
    MARK_READ_ENDPOINT: (chatId) => `/chats/${chatId}/read`,
  },

  // WebSocket Events
  EVENTS: {
    // Outgoing events
    SEND_MESSAGE: 'send_message',
    MARK_READ: 'mark_read',
    TYPING_START: 'typing_start',
    TYPING_STOP: 'typing_stop',
    PING: 'ping',

    // Incoming events
    NEW_MESSAGE: 'new_message',
    MESSAGES_READ: 'messages_read',
    USER_TYPING: 'user_typing',
    CONNECTED: 'connected',
    PONG: 'pong',
    ERROR: 'error',

    // Connection events
    CONNECT: 'connect',
    DISCONNECT: 'disconnect',
    CONNECT_ERROR: 'connect_error',
    RECONNECT: 'reconnect',
    RECONNECT_ERROR: 'reconnect_error',
    RECONNECT_FAILED: 'reconnect_failed',
  },

  // Error Messages
  ERRORS: {
    CONNECTION_FAILED: 'Failed to connect to chat server',
    MESSAGE_TOO_LONG: `Message too long (max ${2000} characters)`,
    MESSAGE_EMPTY: 'Message cannot be empty',
    CHAT_NOT_FOUND: 'Chat not found',
    UNAUTHORIZED: 'You are not authorized to access this chat',
    RATE_LIMITED: 'You are sending messages too quickly. Please slow down.',
    NETWORK_ERROR: 'Network error. Please check your connection.',
    SERVER_ERROR: 'Server error. Please try again later.',
    INVALID_CHAT_TYPE: 'Invalid chat type',
    TOO_MANY_PARTICIPANTS: `Too many participants (max ${50})`,
    CHAT_NAME_TOO_LONG: `Chat name too long (max ${100} characters)`,
    CHAT_NAME_REQUIRED: 'Chat name is required for group chats',
  },

  // Success Messages
  SUCCESS: {
    MESSAGE_SENT: 'Message sent successfully',
    CHAT_CREATED: 'Chat created successfully',
    MESSAGES_MARKED_READ: 'Messages marked as read',
    CONNECTION_ESTABLISHED: 'Connected to chat server',
  },

  // Feature Flags
  FEATURES: {
    TYPING_INDICATORS: true,
    READ_RECEIPTS: true,
    MESSAGE_REACTIONS: false, // Future feature
    FILE_UPLOADS: false, // Future feature
    MESSAGE_EDITING: false, // Future feature
    MESSAGE_DELETION: false, // Future feature
    GROUP_CHATS: true,
    DIRECT_CHATS: true,
    DESKTOP_NOTIFICATIONS: true,
    SOUND_NOTIFICATIONS: false,
    MESSAGE_SEARCH: false, // Future feature
    CHAT_THEMES: false, // Future feature
  },

  // Validation Rules
  VALIDATION: {
    USERNAME_MIN_LENGTH: 3,
    USERNAME_MAX_LENGTH: 30,
    PARTICIPANT_IDS_REQUIRED: true,
    CONTENT_SANITIZATION: true,
  },

  // Performance Settings
  PERFORMANCE: {
    VIRTUAL_SCROLLING: false, // Can be enabled for large message lists
    LAZY_LOADING: true,
    DEBOUNCE_SEARCH: 300,
    THROTTLE_SCROLL: 100,
    BATCH_MESSAGE_UPDATES: true,
    MAX_CONCURRENT_REQUESTS: 3,
  },

  // Security Settings
  SECURITY: {
    SANITIZE_HTML: true,
    VALIDATE_MESSAGE_CONTENT: true,
    VALIDATE_CHAT_NAMES: true,
    PREVENT_XSS: true,
    RATE_LIMIT_ENFORCEMENT: true,
  },
};

// Helper function to get API URL
export const getApiUrl = (endpoint) => {
  return `${CHAT_CONFIG.API.BASE_URL}${endpoint}`;
};

// Helper function to get WebSocket URL
export const getWebSocketUrl = () => {
  const baseUrl = CHAT_CONFIG.API.BASE_URL;
  const wsUrl = baseUrl.replace(/^http/, 'ws');
  return `${wsUrl}${CHAT_CONFIG.WEBSOCKET.NAMESPACE}`;
};

// Helper function to validate message content
export const validateMessage = (content) => {
  if (!content || content.trim().length === 0) {
    return { valid: false, error: CHAT_CONFIG.ERRORS.MESSAGE_EMPTY };
  }

  if (content.length > CHAT_CONFIG.MESSAGES.MAX_LENGTH) {
    return { valid: false, error: CHAT_CONFIG.ERRORS.MESSAGE_TOO_LONG };
  }

  return { valid: true };
};

// Helper function to validate chat name
export const validateChatName = (name, isGroup = false) => {
  if (isGroup && (!name || name.trim().length === 0)) {
    return { valid: false, error: CHAT_CONFIG.ERRORS.CHAT_NAME_REQUIRED };
  }

  if (name && name.length > CHAT_CONFIG.CHATS.MAX_NAME_LENGTH) {
    return { valid: false, error: CHAT_CONFIG.ERRORS.CHAT_NAME_TOO_LONG };
  }

  return { valid: true };
};

// Helper function to validate participants
export const validateParticipants = (participantIds) => {
  if (!Array.isArray(participantIds) || participantIds.length === 0) {
    return { valid: false, error: 'At least one participant is required' };
  }

  if (participantIds.length > CHAT_CONFIG.CHATS.MAX_PARTICIPANTS) {
    return { valid: false, error: CHAT_CONFIG.ERRORS.TOO_MANY_PARTICIPANTS };
  }

  return { valid: true };
};

export default CHAT_CONFIG;
