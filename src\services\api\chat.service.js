import { api } from './api';
import { CHAT_CONFIG, validateMessage, validateChatName, validateParticipants } from '../../config/chat.config.js';

/**
 * HTTP API service for chat functionality
 * Handles REST API calls for chat operations
 */
class ChatService {
  constructor() {
    this.rateLimitTracker = [];
  }

  /**
   * Get user's chats with pagination
   * @param {number} limit - Number of chats to fetch
   * @param {string} cursor - Pagination cursor
   * @returns {Promise<Object>} Paginated chats response
   */
  async getChats(limit = CHAT_CONFIG.CHATS.PAGE_SIZE, cursor = null) {
    try {
      const params = new URLSearchParams();
      params.append('limit', Math.min(limit, CHAT_CONFIG.CHATS.MAX_PAGE_SIZE));
      
      if (cursor) {
        params.append('cursor', cursor);
      }

      const response = await api.get(`${CHAT_CONFIG.API.CHATS_ENDPOINT}?${params}`);
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to fetch chats:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get messages for a specific chat with pagination
   * @param {string} chatId - Chat ID
   * @param {number} limit - Number of messages to fetch
   * @param {string} cursor - Pagination cursor
   * @returns {Promise<Object>} Paginated messages response
   */
  async getMessages(chatId, limit = CHAT_CONFIG.MESSAGES.PAGE_SIZE, cursor = null) {
    try {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }

      const params = new URLSearchParams();
      params.append('limit', Math.min(limit, CHAT_CONFIG.MESSAGES.MAX_PAGE_SIZE));
      
      if (cursor) {
        params.append('cursor', cursor);
      }

      const response = await api.get(`${CHAT_CONFIG.API.MESSAGES_ENDPOINT(chatId)}?${params}`);
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to fetch messages:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Create a new chat
   * @param {Object} chatData - Chat creation data
   * @param {string} chatData.type - Chat type ('direct' or 'group')
   * @param {number[]} chatData.participantIds - Array of participant user IDs
   * @param {string} chatData.name - Chat name (required for groups)
   * @param {string} chatData.description - Chat description (optional)
   * @returns {Promise<Object>} Created chat object
   */
  async createChat(chatData) {
    try {
      // Validate input data
      const { type, participantIds, name, description } = chatData;

      if (!type || !['direct', 'group'].includes(type)) {
        throw new Error(CHAT_CONFIG.ERRORS.INVALID_CHAT_TYPE);
      }

      const participantsValidation = validateParticipants(participantIds);
      if (!participantsValidation.valid) {
        throw new Error(participantsValidation.error);
      }

      const nameValidation = validateChatName(name, type === 'group');
      if (!nameValidation.valid) {
        throw new Error(nameValidation.error);
      }

      const payload = {
        type,
        participantIds,
        ...(name && { name: name.trim() }),
        ...(description && { description: description.trim() }),
      };

      const response = await api.post(CHAT_CONFIG.API.CHATS_ENDPOINT, payload);
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to create chat:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Send a message via HTTP (fallback when WebSocket is unavailable)
   * @param {string} chatId - Chat ID
   * @param {string} content - Message content
   * @param {string} type - Message type (default: 'text')
   * @returns {Promise<Object>} Sent message object
   */
  async sendMessage(chatId, content, type = 'text') {
    try {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }

      // Check rate limiting
      if (!this.checkRateLimit()) {
        throw new Error(CHAT_CONFIG.ERRORS.RATE_LIMITED);
      }

      // Validate message content
      const validation = validateMessage(content);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      const payload = {
        content: content.trim(),
        type,
      };

      const response = await api.post(CHAT_CONFIG.API.SEND_MESSAGE_ENDPOINT(chatId), payload);
      this.trackRateLimit();
      
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to send message:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Mark messages as read
   * @param {string} chatId - Chat ID
   * @returns {Promise<Object>} Response object
   */
  async markAsRead(chatId) {
    try {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }

      const response = await api.put(CHAT_CONFIG.API.MARK_READ_ENDPOINT(chatId));
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to mark messages as read:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get chat details by ID
   * @param {string} chatId - Chat ID
   * @returns {Promise<Object>} Chat object
   */
  async getChat(chatId) {
    try {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }

      const response = await api.get(`${CHAT_CONFIG.API.CHATS_ENDPOINT}/${chatId}`);
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to fetch chat:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Search for users to add to chats
   * @param {string} query - Search query
   * @param {number} limit - Number of results to return
   * @returns {Promise<Object[]>} Array of user objects
   */
  async searchUsers(query, limit = 10) {
    try {
      if (!query || query.trim().length < 2) {
        return [];
      }

      const params = new URLSearchParams();
      params.append('q', query.trim());
      params.append('limit', limit);

      const response = await api.get(`/api/users/search?${params}`);
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to search users:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Add participants to a group chat
   * @param {string} chatId - Chat ID
   * @param {number[]} participantIds - Array of user IDs to add
   * @returns {Promise<Object>} Updated chat object
   */
  async addParticipants(chatId, participantIds) {
    try {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }

      const participantsValidation = validateParticipants(participantIds);
      if (!participantsValidation.valid) {
        throw new Error(participantsValidation.error);
      }

      const payload = { participantIds };
      const response = await api.post(`${CHAT_CONFIG.API.CHATS_ENDPOINT}/${chatId}/participants`, payload);
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to add participants:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Remove participants from a group chat
   * @param {string} chatId - Chat ID
   * @param {number[]} participantIds - Array of user IDs to remove
   * @returns {Promise<Object>} Updated chat object
   */
  async removeParticipants(chatId, participantIds) {
    try {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }

      if (!Array.isArray(participantIds) || participantIds.length === 0) {
        throw new Error('At least one participant ID is required');
      }

      const payload = { participantIds };
      const response = await api.delete(`${CHAT_CONFIG.API.CHATS_ENDPOINT}/${chatId}/participants`, { data: payload });
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to remove participants:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Update chat settings (name, description, etc.)
   * @param {string} chatId - Chat ID
   * @param {Object} updates - Updates to apply
   * @returns {Promise<Object>} Updated chat object
   */
  async updateChat(chatId, updates) {
    try {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }

      // Validate name if provided
      if (updates.name !== undefined) {
        const nameValidation = validateChatName(updates.name, true);
        if (!nameValidation.valid) {
          throw new Error(nameValidation.error);
        }
        updates.name = updates.name.trim();
      }

      // Trim description if provided
      if (updates.description !== undefined) {
        updates.description = updates.description.trim();
      }

      const response = await api.patch(`${CHAT_CONFIG.API.CHATS_ENDPOINT}/${chatId}`, updates);
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to update chat:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Leave a chat
   * @param {string} chatId - Chat ID
   * @returns {Promise<Object>} Response object
   */
  async leaveChat(chatId) {
    try {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }

      const response = await api.delete(`${CHAT_CONFIG.API.CHATS_ENDPOINT}/${chatId}/leave`);
      return response.data;
    } catch (error) {
      console.error('ChatService: Failed to leave chat:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Check HTTP rate limiting
   * @returns {boolean} Whether request is allowed
   */
  checkRateLimit() {
    if (CHAT_CONFIG.DEV.DISABLE_RATE_LIMITING) {
      return true;
    }

    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Remove old entries
    this.rateLimitTracker = this.rateLimitTracker.filter(timestamp => timestamp > oneMinuteAgo);

    return this.rateLimitTracker.length < CHAT_CONFIG.RATE_LIMITS.HTTP_MESSAGES_PER_MINUTE;
  }

  /**
   * Track rate limit usage
   */
  trackRateLimit() {
    this.rateLimitTracker.push(Date.now());
  }

  /**
   * Handle API errors and convert them to user-friendly messages
   * @param {Error} error - API error
   * @returns {Error} Processed error
   */
  handleApiError(error) {
    // Handle network errors
    if (!error.response) {
      return new Error(CHAT_CONFIG.ERRORS.NETWORK_ERROR);
    }

    const { status, data } = error.response;

    switch (status) {
      case 400:
        return new Error(data?.message || 'Invalid request');
      case 401:
        return new Error(CHAT_CONFIG.ERRORS.UNAUTHORIZED);
      case 403:
        return new Error('You do not have permission to perform this action');
      case 404:
        return new Error(CHAT_CONFIG.ERRORS.CHAT_NOT_FOUND);
      case 429:
        return new Error(CHAT_CONFIG.ERRORS.RATE_LIMITED);
      case 500:
      case 502:
      case 503:
      case 504:
        return new Error(CHAT_CONFIG.ERRORS.SERVER_ERROR);
      default:
        return new Error(data?.message || 'An unexpected error occurred');
    }
  }

  /**
   * Clear rate limit tracker (useful for testing)
   */
  clearRateLimit() {
    this.rateLimitTracker = [];
  }

  /**
   * Get rate limit status
   * @returns {Object} Rate limit information
   */
  getRateLimitStatus() {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    const recentRequests = this.rateLimitTracker.filter(timestamp => timestamp > oneMinuteAgo);

    return {
      requestsInLastMinute: recentRequests.length,
      maxRequestsPerMinute: CHAT_CONFIG.RATE_LIMITS.HTTP_MESSAGES_PER_MINUTE,
      remainingRequests: Math.max(0, CHAT_CONFIG.RATE_LIMITS.HTTP_MESSAGES_PER_MINUTE - recentRequests.length),
      resetTime: recentRequests.length > 0 ? new Date(Math.min(...recentRequests) + 60000) : new Date(),
    };
  }
}

// Create and export singleton instance
export const chatService = new ChatService();
export default chatService;
